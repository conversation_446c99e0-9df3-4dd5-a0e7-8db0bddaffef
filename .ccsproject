<?xml version="1.0" encoding="UTF-8" ?>
<?ccsproject version="1.0"?>
<projectOptions>
	<ccsVariant value="50:Theia-based"/>
	<ccsVersion value="51.5.0"/>
	<deviceFamily value="TMS470"/>
	<connection value="common/targetdb/connections/uart_connection.xml"/>
	<createSlaveProjects value=""/>
	<ignoreDefaultDeviceSettings value="true"/>
	<ignoreDefaultCCSSettings value="true"/>
	<templateProperties value="id=empty_mspm0g3507_nortos_ticlang.projectspec.empty_mspm0g3507_nortos_ticlang,buildProfile=release,isHybrid=true"/>
	<activeTargetConfiguration value="targetConfigs/MSPM0G3507.ccxml"/>
	<isTargetConfigurationManual value="false"/>
	<sourceLookupPath value="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib"/>
	<origin value="E:\SoftWare\ti\mspm0_sdk_2_02_00_05\examples\nortos\CUSTOM_BOARD\driverlib\empty_mspm0g3507\ticlang\empty_mspm0g3507_nortos_ticlang.projectspec"/>
	<filesToOpen value="empty_mspm0g3507.syscfg,README.md"/>
</projectOptions>
